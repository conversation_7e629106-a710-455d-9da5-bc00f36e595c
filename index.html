<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Favourite Podcasts</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #ffffff;
            width: 428px;
            height: 926px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .container {
            padding: 48px 32px 0;
            height: 100%;
            position: relative;
        }

        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 80px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            width: 50px;
            height: 46px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #4c0099;
        }

        .notification-container {
            position: relative;
        }

        .notification-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 4px;
            width: 12px;
            height: 12px;
            background-color: #ff5757;
            border-radius: 6px;
        }

        /* Title */
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #1f1f1f;
            margin-bottom: 28px;
        }

        /* Podcast List */
        .podcast-list {
            display: flex;
            flex-direction: column;
            gap: 32px;
            margin-bottom: 120px;
        }

        .podcast-item {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .podcast-image {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            object-fit: cover;
        }

        .podcast-info {
            flex: 1;
        }

        .podcast-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f1f1f;
            margin-bottom: 8px;
        }

        .podcast-category {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
            margin-bottom: 8px;
        }

        .podcast-duration {
            font-size: 14px;
            color: rgba(31, 31, 31, 0.7);
        }

        .play-btn {
            width: 48px;
            height: 48px;
            background-color: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            cursor: pointer;
        }

        .play-icon {
            width: 18px;
            height: 18px;
            background-color: #4c0099;
            clip-path: polygon(0 0, 100% 50%, 0 100%);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: absolute;
            bottom: 32px;
            left: 32px;
            right: 32px;
            height: 72px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 0 20px;
            z-index: 2;
        }

        .nav-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            opacity: 0.5;
            cursor: pointer;
        }

        .nav-item.active {
            opacity: 1;
        }

        .nav-item img {
            width: 24px;
            height: 24px;
        }

        .nav-dot {
            width: 5px;
            height: 5px;
            background-color: #4c0099;
            border-radius: 50%;
            margin-top: 8px;
        }

        /* Gradient overlay */
        .gradient-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 156px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
            pointer-events: none;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-container">
                <img src="assets/logo.svg" alt="NCast Logo" class="logo">
                <span class="logo-text">NCAST</span>
            </div>
            <div class="notification-container">
                <button class="notification-btn">
                    <img src="assets/bell-icon.svg" alt="Notifications" width="21" height="21">
                </button>
                <div class="notification-badge"></div>
            </div>
        </div>

        <!-- Title -->
        <h1 class="title">Favourite Podcasts</h1>

        <!-- Podcast List -->
        <div class="podcast-list">
            <div class="podcast-item">
                <img src="assets/podcast1.png" alt="Sunday Summer - Ep3" class="podcast-image">
                <div class="podcast-info">
                    <div class="podcast-title">Sunday Summer - Ep3</div>
                    <div class="podcast-category">Entertainment</div>
                    <div class="podcast-duration">15 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="assets/podcast2.png" alt="Musical Soul - Vol. 1" class="podcast-image">
                <div class="podcast-info">
                    <div class="podcast-title">Musical Soul - Vol. 1</div>
                    <div class="podcast-category">Lifestyle</div>
                    <div class="podcast-duration">35 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="assets/podcast3.png" alt="Talk Show - Ep4" class="podcast-image">
                <div class="podcast-info">
                    <div class="podcast-title">Talk Show - Ep4</div>
                    <div class="podcast-category">Business</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="assets/podcast4.png" alt="Musical Soul - Vol. 2" class="podcast-image">
                <div class="podcast-info">
                    <div class="podcast-title">Musical Soul - Vol. 2</div>
                    <div class="podcast-category">Lifestyle</div>
                    <div class="podcast-duration">30 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="assets/podcast5.png" alt="Unravelling The Mind" class="podcast-image">
                <div class="podcast-info">
                    <div class="podcast-title">Unravelling The Mind</div>
                    <div class="podcast-category">Healthy Lifestyle</div>
                    <div class="podcast-duration">10 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>

            <div class="podcast-item">
                <img src="assets/podcast6.png" alt="Talk Show - Ep8" class="podcast-image">
                <div class="podcast-info">
                    <div class="podcast-title">Talk Show - Ep8</div>
                    <div class="podcast-category">Entertainment</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn">
                    <div class="play-icon"></div>
                </button>
            </div>
        </div>

        <!-- Gradient Overlay -->
        <div class="gradient-overlay"></div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item">
                <img src="assets/headphones-icon.svg" alt="Listen">
            </div>
            <div class="nav-item">
                <img src="assets/compass-icon.svg" alt="Discover">
            </div>
            <div class="nav-item active">
                <img src="assets/heart-icon.svg" alt="Favorites">
            </div>
            <div class="nav-item">
                <img src="assets/profile-icon.svg" alt="Profile">
            </div>
        </div>
        <div style="position: absolute; bottom: 16px; left: 50%; transform: translateX(-50%); z-index: 3;">
            <div class="nav-dot"></div>
        </div>
    </div>
</body>
</html>